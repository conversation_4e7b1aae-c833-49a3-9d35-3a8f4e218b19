<?php
/**
 * User Login Endpoint
 */

validateRequired($input, ['token']);

$auth = new Auth();
$token = sanitizeInput($input['token']);

// Check if it's a one-time auth token
$userId = $auth->useAuthToken($token);

if ($userId) {
    // One-time token authentication
    $result = $auth->authenticateWithToken($token);
    if ($result) {
        // Create session
        $deviceInfo = $input['device_info'] ?? null;
        $sessionToken = $auth->createSession($userId, $deviceInfo);
        
        $response['success'] = true;
        $response['message'] = 'Login successful';
        $response['data'] = [
            'user' => $result['user'],
            'jwt_token' => $result['jwt_token'],
            'session_token' => $sessionToken
        ];
        
        // Log activity
        logActivity("User {$userId} logged in successfully", 'INFO');
    } else {
        $response['message'] = 'Invalid authentication token';
        http_response_code(401);
    }
} else {
    // Regular unique token authentication
    $result = $auth->authenticateWithToken($token);
    
    if ($result) {
        // Create session
        $deviceInfo = $input['device_info'] ?? null;
        $sessionToken = $auth->createSession($result['user']['id'], $deviceInfo);
        
        $response['success'] = true;
        $response['message'] = 'Login successful';
        $response['data'] = [
            'user' => $result['user'],
            'jwt_token' => $result['jwt_token'],
            'session_token' => $sessionToken
        ];
        
        // Log activity
        logActivity("User {$result['user']['id']} logged in successfully", 'INFO');
    } else {
        $response['message'] = 'Invalid authentication token';
        http_response_code(401);
    }
}

echo json_encode($response);
?>
