<?php
/**
 * Application Configuration
 */

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

// Application settings
define('APP_NAME', 'Weight Loss Dashboard');
define('APP_VERSION', '1.0.0');
define('APP_URL', $_ENV['APP_URL'] ?? 'http://localhost');

// Security settings
define('JWT_SECRET', $_ENV['JWT_SECRET'] ?? 'your-secret-key-change-this');
define('JWT_ALGORITHM', 'HS256');
define('JWT_EXPIRY', 86400 * 30); // 30 days

// Vimeo settings
define('VIMEO_ACCESS_TOKEN', $_ENV['VIMEO_ACCESS_TOKEN'] ?? '');
define('VIMEO_CLIENT_ID', $_ENV['VIMEO_CLIENT_ID'] ?? '');
define('VIMEO_CLIENT_SECRET', $_ENV['VIMEO_CLIENT_SECRET'] ?? '');

// File upload settings
define('MAX_FILE_SIZE', 100 * 1024 * 1024); // 100MB
define('UPLOAD_PATH', __DIR__ . '/../uploads/');

// Session settings (only if session not started)
if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', 0); // Set to 0 for localhost development
    ini_set('session.use_strict_mode', 1);
}

// Error reporting
if ($_ENV['APP_ENV'] === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Timezone
date_default_timezone_set($_ENV['TIMEZONE'] ?? 'UTC');

// CORS settings for API
function setCorsHeaders() {
    $allowed_origins = [
        'http://localhost:3000',
        'http://localhost:8080',
        'https://your-domain.com'
    ];
    
    $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
    
    if (in_array($origin, $allowed_origins)) {
        header("Access-Control-Allow-Origin: $origin");
    }
    
    header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
    header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
    header("Access-Control-Allow-Credentials: true");
    
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit();
    }
}

// Helper functions
function generateUniqueToken($length = 32) {
    return bin2hex(random_bytes($length));
}

function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

function logActivity($message, $level = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] [$level] $message" . PHP_EOL;
    file_put_contents(__DIR__ . '/../logs/app.log', $log_entry, FILE_APPEND | LOCK_EX);
}

// Create necessary directories
$directories = [
    __DIR__ . '/../uploads',
    __DIR__ . '/../logs',
    __DIR__ . '/../cache'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}
?>
